import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import ReactCodeMirror from '@uiw/react-codemirror';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import { gsql } from '@/pages/editor/GSQL/script';
import { useMemo } from 'react';
import { MdContentCopy } from 'react-icons/md';
import IconButton from '@/components/IconButton';
import { LoadingIndicator } from '@/components/loading-indicator';
import { AlgorithmItem, useAlgorithmCode } from '@/pages/editor/scienceLibrary/useGraphAlgorithms';
import clsx from 'clsx';
import InstallGDSPopover from './InstallGDSPopover';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { EditorView } from '@codemirror/view';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { PLACEMENT } from 'baseui/popover';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { BsBook } from 'react-icons/bs';
import { ListItem } from '@/components/Expandable';
import { useEditorContext } from '@/contexts/graphEditorContext';

interface AlgorithmPopoverContentProps {
  algorithm: AlgorithmItem;
  graphName: string;
  createTempFile: CreateTempFileFn;
  onClose?: () => void;
}

export default function AlgorithmPopoverContent({
  algorithm,
  graphName,
  createTempFile,
  onClose,
}: AlgorithmPopoverContentProps) {
  const [css, theme] = useStyletron();
  const editorTheme = useEditorTheme();
  const { data: algorithmCode, isLoading, error } = useAlgorithmCode(algorithm.algorithmName!);
  const { currentFileId } = useEditorContext();

  const cmExtensions = useMemo(() => {
    // Encounter an issue when loading extensions in test environment
    if (import.meta.env.VITEST) {
      return [];
    }
    return [gsql()];
  }, []);

  const handleEditInNewFile = () => {
    if (algorithmCode) {
      createTempFile(false, algorithm.name, algorithmCode, graphName);
      onClose?.();
    }
  };

  const handleCopyToCurrentFile = () => {
    if (algorithmCode) {
      const cmView = (window as any).cmView as EditorView;
      if (cmView) {
        cmView.dispatch({
          changes: {
            from: cmView.state.doc.length,
            insert: `\n${algorithmCode}`,
          },
        });
        showToast({ kind: 'positive', message: 'Algorithm code copied to current file' });
      } else {
        showToast({ kind: 'negative', message: 'No editor is currently open' });
      }
      onClose?.();
    }
  };

  const hasOpenedFile = currentFileId && (window as any).cmView;

  // Action buttons
  const operations = [
    {
      label: 'Learn',
      icon: <BsBook size={16} color={theme.colors['icon.primary']} />,
      handleFn: () => {
        window.open('https://docs.tigergraph.com/graph-ml/3.10/intro/', '_blank');
      },
    },
    {
      label: 'Copy',
      icon: <MdContentCopy size={16} color={theme.colors['icon.primary']} />,
      handleFn: () => {},
      disabled: !algorithmCode,
    },
  ];

  return (
    <div className={css({ width: '393px', display: 'flex', flexDirection: 'column', gap: '12px' })}>
      <div className={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
        <div
          className={clsx(
            'w-[40px] h-[16px] flex items-center justify-center rounded-[1px] font-[700] text-[10px]',
            css({
              color: theme.colors['text.inverse'],
              background: theme.colors['background.accent.gray.bolder'],
            })
          )}
        >
          GDS
        </div>
        <span className={css({ color: theme.colors['text.primary'], fontSize: '12px', fontWeight: 700 })}>
          {algorithm.name}
        </span>
      </div>

      {algorithm.description && (
        <div className={css({ fontSize: '12px', color: theme.colors['text.secondary'] })}>{algorithm.description}</div>
      )}

      {algorithm.schemaConstraints && (
        <div>
          <div
            className={css({
              fontSize: '12px',
              fontWeight: 600,
              marginBottom: '4px',
              color: theme.colors['text.primary'],
            })}
          >
            Schema Constraints
          </div>
          <div className={css({ fontSize: '12px', color: theme.colors['text.secondary'] })}>
            {algorithm.schemaConstraints}
          </div>
        </div>
      )}

      <div>
        {isLoading ? (
          <div className={css({ display: 'flex', justifyContent: 'center', padding: '20px' })}>
            <LoadingIndicator />
          </div>
        ) : error ? (
          <div className={css({ color: theme.colors['text.warning'], padding: '8px', fontSize: '12px' })}>
            {(error as Error).message || 'Failed to fetch algorithm code'}
          </div>
        ) : (
          <ReactCodeMirror
            value={algorithmCode || ''}
            readOnly={true}
            maxWidth={'100%'}
            height="auto"
            maxHeight="300px"
            theme={editorTheme}
            extensions={cmExtensions}
          />
        )}
      </div>

      <div className="flex justify-end gap-2 align-center">
        {operations.map((op) =>
          op.label !== 'Copy' ? (
            <IconButton key={op.label} title={op.label} disabled={op.disabled} onClick={op.handleFn} type="button">
              {op.icon}
            </IconButton>
          ) : (
            <StatefulPopover
              key={op.label}
              content={({ close }) => (
                <div>
                  <ListItem
                    onClick={() => {
                      handleEditInNewFile();
                      close();
                    }}
                  >
                    Copy to New File
                  </ListItem>
                  <ListItem
                    onClick={() => {
                      handleCopyToCurrentFile();
                      close();
                    }}
                    style={{
                      opacity: hasOpenedFile ? 1 : 0.5,
                      cursor: hasOpenedFile ? 'pointer' : 'not-allowed',
                      pointerEvents: hasOpenedFile ? 'auto' : 'none',
                    }}
                  >
                    Copy to Current File
                  </ListItem>
                </div>
              )}
              placement={PLACEMENT.bottom}
              ignoreBoundary
              showArrow={false}
            >
              <IconButton title={op.label} disabled={op.disabled} type="button">
                {op.icon}
              </IconButton>
            </StatefulPopover>
          )
        )}
        <InstallGDSPopover algorithm={algorithm} />
      </div>
    </div>
  );
}
