import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { AlgorithmItem, useGraphAlgorithms } from '@/pages/editor/scienceLibrary/useGraphAlgorithms';
import { LoadingIndicator } from '@/components/loading-indicator';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { AlgorithmCategoryIcon, AlgorithmIcon } from '@/pages/home/<USER>';
import { PLACEMENT } from 'baseui/popover';
import AlgorithmPopoverContent from './AlgorithmPopoverContent';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';

/**
 * Renders a category label with an icon
 */
function CategoryLabel({ name }: { name: string }) {
  const [css, theme] = useStyletron();

  return <ListItemLabel icon={<AlgorithmCategoryIcon />} label={name} />;
}

/**
 * Renders an algorithm item with an icon
 */
function AlgorithmLabel({ algo }: { algo: AlgorithmItem }) {
  const [css, theme] = useStyletron();

  return <ListItemLabel icon={<AlgorithmIcon />} label={algo.name} />;
}

/**
 * Recursively renders algorithm categories and items using Expandable components
 */
function RenderAlgorithmTree({
  items,
  currentGraph,
  createTempFile,
}: {
  items: AlgorithmItem[];
  currentGraph: string;
  createTempFile: CreateTempFileFn;
}) {
  return (
    <>
      {items.map((item, index) => {
        const hasChildren = (item.subs && item.subs.length > 0) || (item.algorithms && item.algorithms.length > 0);

        if (hasChildren) {
          return (
            <Expandable key={`${item.name}-${index}`} label={<CategoryLabel name={item.name} />}>
              {/* Render algorithm items */}
              {item.algorithms &&
                item.algorithms.map((algo, algoIndex) => (
                  <StatefulPopover
                    key={`${algo.name}-${algoIndex}`}
                    content={({ close }) => (
                      <AlgorithmPopoverContent
                        algorithm={algo}
                        graphName={currentGraph || ''}
                        createTempFile={createTempFile}
                        onClose={close}
                      />
                    )}
                    placement={PLACEMENT.right}
                    ignoreBoundary
                    animateOutTime={200}
                    dismissOnClickOutside
                  >
                    <ListItem>
                      <AlgorithmLabel algo={algo} />
                    </ListItem>
                  </StatefulPopover>
                ))}

              {/* Render subcategories recursively */}
              {item.subs && (
                <RenderAlgorithmTree items={item.subs} currentGraph={currentGraph} createTempFile={createTempFile} />
              )}
            </Expandable>
          );
        } else if (item.algorithms) {
          // Render algorithms without subcategories
          return item.algorithms.map((algo, algoIndex) => (
            <StatefulPopover
              key={`${algo.name}-${algoIndex}`}
              content={({ close }) => (
                <AlgorithmPopoverContent
                  algorithm={algo}
                  graphName={currentGraph || ''}
                  createTempFile={createTempFile}
                  onClose={close}
                />
              )}
              placement={PLACEMENT.right}
              ignoreBoundary
              animateOutTime={200}
              dismissOnClickOutside
            >
              <ListItem>
                <AlgorithmLabel algo={algo} />
              </ListItem>
            </StatefulPopover>
          ));
        }

        return null;
      })}
    </>
  );
}

interface GraphAlgorithmsProps {
  createTempFile: CreateTempFileFn;
}

export default function GraphAlgorithms({ createTempFile }: GraphAlgorithmsProps) {
  const [css, theme] = useStyletron();
  const { currentWorkspace, currentGraph } = useWorkspaceContext();
  const { data: algorithms, isLoading, error } = useGraphAlgorithms(currentWorkspace);

  if (isLoading) {
    return (
      <div className={css({ padding: '16px', display: 'flex', justifyContent: 'center' })}>
        <LoadingIndicator />
      </div>
    );
  }

  return (
    <div>
      <Expandable
        label={<ListItemLabel icon={<AlgorithmCategoryIcon />} label={'Graph Data Science Library'} />}
        defaultExpanded={true}
        contentPadding="12px"
      >
        <RenderAlgorithmTree
          items={algorithms || []}
          currentGraph={currentGraph || ''}
          createTempFile={createTempFile}
        />
      </Expandable>
    </div>
  );
}
